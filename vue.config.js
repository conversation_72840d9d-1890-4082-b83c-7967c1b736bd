const path = require('path');
const { defineConfig } = require('@vue/cli-service');
const setting = require('./src/config.js');
const CompressionPlugin = require('compression-webpack-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
// const BundleAnalyzerPlugin = require( 'webpack-bundle-analyzer').BundleAnalyzerPlugin;

const name = setting.title || '天易开发平台';
function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = defineConfig({
  transpileDependencies: ['sn-base-layout'],
  publicPath: setting.publicPath,
  outputDir: process.env.VUE_APP_OUTPUT_DIR,
  assetsDir: 'static',
  // eslint-disable-next-line no-dupe-keys
  outputDir: 'deepAdmin',
  productionSourceMap: process.env.VUE_APP_PRODUCT_SOURCE_MAP != 'false',
  lintOnSave: false,
  pages: {
    index: {
      // 修改项目入口文件
      entry: 'src/main.js',
      template: 'public/index.html',
      filename: 'index.html',
    },
  },
  devServer: {
    client: {
      overlay: false,
    },
    port: process.env.VUE_APP_PORT,
    headers: {
      'Access-Control-Allow-Credentials': true,
      'Access-Control-Allow-Origin': '*',
    },
    open: false,
    allowedHosts: 'all',
    // 接口转发
    // 王城瑞 ：*************
    // 韦德祥： *************
    proxy: {
      [process.env.VUE_APP_PROXY]: {
        // target: 'http://*************:9111/',//韦德祥
        target: 'http://*************:9111/', //王城瑞
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_PROXY]: '',
        },
      },
      [process.env.VUE_APP_BASE_API]: {
        target: setting.URI,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: '',
        },
      },
      '/webMessage': {
        target: setting.isCloud ? setting.URI + '/message' : setting.URI,
        changeOrigin: true,
      },
      '/miniofile': {
        target: 'http://minio.dev.snpit.com:9000/',
        changeOrigin: true,
        pathRewrite: {
          ['^/miniofile']: '',
        },
      },
    },
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: 'expanded' },
      },
    },
  },
  configureWebpack: {
    name: name,
    externals:
      process.env.VUE_APP_OUTPUT_DIR == 'dist'
        ? {}
        : {
            vue: {
              root: 'Vue',
              commonjs2: 'vue',
              commonjs: 'vue',
              amd: 'vue',
            },
            'sn-element': {
              root: 'ELEMENT',
              commonjs2: 'sn-element',
              commonjs: 'sn-element',
              amd: 'sn-element',
            },
            axios: 'axios',
            'sn-base-utils': 'sn-base-utils',
          },
    resolve: {
      alias: {
        '@': resolve('src'),
        '@common': resolve('src/common'),
        '@packLayout': 'sn-base-layout/packLayout',
      },
    },
    output: {
      library: `${process.env.VUE_APP_APPLATION_NAME}-[name]`,
      libraryTarget: 'umd', // 把微应用打包成 umd 库格式
      chunkLoadingGlobal: `webpackJsonp_${process.env.VUE_APP_APPLATION_NAME}`, // webpack 5 需要把 jsonpFunction 替换成 chunkLoadingGlobal
    },
    plugins: [
      // new BundleAnalyzerPlugin(),
      new NodePolyfillPlugin(),
      new CompressionPlugin({
        test: /\.(js|css|html)?$/i, // 压缩文件格式
        filename: '[path][base].gz', // 压缩后的文件名
        algorithm: 'gzip', // 使用gzip压缩
        minRatio: 0.8, // 压缩率小于1才会压缩
      }),
    ],
  },
  chainWebpack(config) {
    config.module
      .rule('js')
      .test(/\.js$/)
      .include.add(resolve('src'))
      .add(resolve('src'))
      .end()
      .include.add(resolve('node_modules/sn-base-layout'))
      .end()
      .use('babel')
      .loader('babel-loader')
      .options({
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false,
            },
          ],
        ],
      });
    config.module
      .rule('js')
      .test(/\.(js|jsx)$/)
      .include.add(resolve('packLayout'))
      .add(resolve('packLayout'))
      .end()
      .use('babel')
      .loader('babel-loader')
      .options({
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false,
            },
          ],
        ],
      });
    // eslint-disable-next-line no-undef
    config.module
      .rule('xml')
      .test(/\.xml$/)
      .include.add(resolve('node_modules'))
      .add(resolve('src'))
      .end()
      .use('xml-loader')
      .loader('xml-loader')
      .end();
    // config.module
    //   .rule("svg")
    //   .exclude.add(resolve("src/common/assets/icons"))
    //   .end();
    // config.module
    //   .rule("icons")
    //   .test(/\.svg$/)
    //   .include.add(resolve("src/common/assets/icons"))
    //   .end()
    //   .use("svg-sprite-loader")
    //   .loader("svg-sprite-loader")
    //   .options({
    //     symbolId: "icon-[name]",
    //   })
    //   .end();
    config.when(process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'lib', () => {
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            //指定chunks名称
            name: 'chunk-libs',
            //符合组的要求就给构建venders
            test: /[\\/]node_modules[\\/]/,
            //priority:优先级：数字越大优先级越高，因为默认值为0，所以自定义的一般是负数形式,决定cacheGroups中相同条件下每个组执行的优先顺序。
            priority: 10,
            // 仅限于最初依赖的第三方
            chunks: 'initial',
          },
          elementUI: {
            // 将elementUI拆分为单个包
            name: 'chunk-elementUI',
            // 重量需要大于libs和app，否则将打包到libs或app中
            priority: 20,
            // 为了适应cnpm
            test: /[\\/]node_modules[\\/]_?sn-element(.*)/,
          },
          snpitUI: {
            // 将elementUI拆分为单个包
            name: 'chunk-snpitUI',
            // 重量需要大于libs和app，否则将打包到libs或app中
            priority: 20,
            // 为了适应cnpm
            test: /[\\/]node_modules[\\/]_?snpit-ui(.*)/,
          },
          system: {
            name: 'chunk-snAppSystem',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?sn-app-system(.*)/,
          },
          layout: {
            name: 'chunk-snBaseLayout',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?sn-base-layout(.*)/,
          },
          pluginModules: {
            name: 'chunk-pluginModules',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?sn-plugin-modules(.*)/,
          },
          snBpmV2: {
            // 将elementUI拆分为单个包
            name: 'chunk-snBpmV2',
            // 重量需要大于libs和app，否则将打包到libs或app中
            priority: 20,
            // 为了适应cnpm
            test: /[\\/]node_modules[\\/]_?sn-bpm-v2(.*)/,
          },
          //公共组件
          commons: {
            name: 'chunk-commons',
            // eslint-disable-next-line no-undef
            test: resolve('src/components'),
            minChunks: 3,
            priority: 30,
            //这个的作用是当前的chunk如果包含了从main里面分离出来的模块，则重用这个模块，这样的问题是会影响chunk的名称。
            reuseExistingChunk: true,
            //最大初始化加载次数，一个入口文件可以并行加载的最大文件数量，默认3
            maxInitialRequests: 3,
            //表示在分离前的最小模块大小，默认为0，最小为30000
            minSize: 0,
          },
          utils: {
            // split utils libs
            name: 'chunk-utils',
            // eslint-disable-next-line no-undef
            test: resolve('src/common/utils'),
            priority: 70,
            chunks: 'async',
            reuseExistingChunk: true,
          },
        },
      });
      config.optimization.runtimeChunk('single');
    });
  },
});
