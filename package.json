{"name": "sn-admin-v2", "version": "2.1.6", "main": "src/main.js", "private": false, "scripts": {"prepare": "husky install", "dev": "cross-env VUE_APP_PORT=8888 VUE_APP_APPLATION_NAME=qaassistant vue-cli-service serve --open", "build": "cross-env VUE_APP_APPLATION_NAME=qaassistant vue-cli-service build", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --cache"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@vue-office/docx": "^1.6.2", "axios": "^1.9.0", "clipboard": "^2.0.11", "codemirror": "^5.65.15", "common_form_item": "^1.1.0", "core-js": "^3.32.1", "echarts": "4.9.0", "highlight.js": "10.5", "husky": "^8.0.0", "js-sha256": "^0.9.0", "jsencrypt": "3.3.2", "json-bigint": "^1.0.0", "json5": "^2.2.3", "jsonlint": "^1.6.3", "mockjs": "^1.1.0", "net": "^1.0.2", "node-rsa": "^1.1.1", "postcss": "^8.2.15", "qiankun": "^2.10.16", "qs": "^6.14.0", "regenerator-runtime": "^0.13.9", "sass": "^1.32.7", "script-loader": "^0.7.2", "sn-base-layout": "2.1.6-hotfix.1", "sn-base-utils": "2.1.6", "sn-element": "^0.3.34", "sn-plugin-modules": "^0.0.1", "snpit-ui": "2.1.6", "sockjs": "^0.3.24", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "svg-sprite-loader": "^6.0.11", "tinymce": "^5.10.9", "tinymce-plugin": "^0.0.3-beta.22", "vue": "^2.7.16", "vue-codemirror": "^4.0.6", "vue-cron": "^1.0.9", "vue-cropper": "^0.6.2", "vue-router": "^3.5.1", "vuex": "^3.6.2", "webpack-bundle-analyzer": "^4.10.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/preset-env": "^7.22.15", "@babel/standalone": "^7.22.17", "@tinymce/tinymce-vue": "^3.2.8", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "ajv": "^8.17.1", "babel-loader": "^9.1.3", "babel-plugin-root-import": "^6.6.0", "babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "es6-promise": "^4.2.8", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "node-polyfill-webpack-plugin": "^2.0.1", "prettier": "^2.4.1", "sass-loader": "^12.0.0", "webpack-dev-server": "^4.11.0", "xml-loader": "^1.2.1"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}