import Vue from 'vue';
import VueRouter from 'vue-router';

const ctx = require.context('@/', true, /router\/index.js/);
let constantRoutes = [];
for (const key of ctx.keys()) {
  constantRoutes = [...constantRoutes, ...ctx(key).default];
}

export { constantRoutes };

Vue.use(VueRouter);

const createRouter = () =>
  new VueRouter({
    mode: 'history',
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  });

const router = createRouter();

export default router;
