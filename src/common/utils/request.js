import axios from 'axios';
import Qs from 'qs';
import { isJsonStr } from './index';
import { Message } from 'element-ui';
import { getToken } from 'sn-base-utils';

// Map对象保存键值对。任何值(对象或者原始值) 都可以作为一个键或一个值。
const pendingRequest = new Map();

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';

/*
调用方式
采用按需传参的方式，即在每个接口的请求头中根据该接口的具体情况设置设置字段使用,如下：
     _cancel: true       // 接口中定义该项则开启取消重复请求功能
*/

// generateReqKey ：用于根据当前请求的信息，生成请求 Key；

const errorCode = [400, 402, 403, 404, 500, 501, 502, 503, 504, 505];
function generateReqKey(config) {
  if (config && config.data && isJsonStr(config.data)) {
    config.data = JSON.parse(config.data);
  }
  const { method, url, params, data } = config; // 请求方式，参数，请求地址，
  return [method, url, Qs.stringify(params), Qs.stringify(data)].join('&'); // 拼接
}

// removePendingRequest：检查是否存在重复请求，若存在则取消已发的请求。
function removePendingRequest(response) {
  if (response && response.config && response.config._cancel) {
    const requestKey = generateReqKey(response.config);
    // 判断是否有这个 key
    if (pendingRequest.has(requestKey)) {
      const cancelToken = pendingRequest.get(requestKey);
      cancelToken(requestKey);
      pendingRequest.delete(requestKey);
    }
  }
}

// addPendingRequest ：用于把当前请求信息添加到pendingRequest对象 中；
function addPendingRequest(config) {
  if (config._cancel) {
    const requestKey = generateReqKey(config);
    if (pendingRequest.has(requestKey)) {
      removePendingRequest({ config });
    }
    config.cancelToken = new axios.CancelToken((cancel) => {
      pendingRequest.set(requestKey, cancel);
    });
  }
}

/**
 * 应用类型
 * @param {*} config
 * @returns
 */

// 请求头拦截方法
function reqInterceptors(config) {
  // 是否需要设置 token
  const token = getToken();
  if (token) {
    config.headers['Authorization'] = 'Bearer ' + token; // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  /*
   *** data 是作为请求主体被发送的数据只适用于这些请求方法 'PUT', 'POST', 和 'PATCH'
   */
  const reqMethod1 = ['put', 'post', 'patch'];
  /*
   *** params 是作为请求主体被发送的数据只适用于这些请求方法 'get', 'delete'
   */
  const reqMethod2 = ['get', 'delete'];

  if (reqMethod1.includes(config.method.toLocaleLowerCase())) {
    config.data = { ...config.data };
    // 参数统一处理，请求都使用data传参
  } else if (reqMethod2.includes(config.method.toLocaleLowerCase())) {
    // 参数统一处理
    config.params = { ...config.params };
  }

  // pendding 中的请求，后续请求不发送（由于存放的peddingMap 的key 和参数有关，所以放在参数处理之后）
  addPendingRequest(config);
  return config;
}

// 响应头拦截
function resInterceptors(response) {
  // 响应正常时候就从pendingRequest对象中移除请求
  removePendingRequest(response);
  const { code, message = '请求失败' } = response.data;
  if ([200].includes(code)) {
    return response.data;
  }
  if ([401].includes(code)) {
    // Handle unauthorized access
    Message({
      message: message,
      type: 'error',
    });
    return;
  }
  if (errorCode.includes(code)) {
    Message({
      message: message,
      type: 'error',
    });
    return;
  }
  return response;
}

// 异常拦截处理器
const errorHandler = (error) => {
  if (error && error.response) {
    // 从pending 列表中移除请求
    removePendingRequest(error || {});
    // 对响应错误做点什么
  } else {
    // 从pending 列表中移除请求
    removePendingRequest(error.config || {});
    // 对请求错误做些什么
  }
  const code = error.status || error.code;
  const msg = error.message || error.msg;
  if (errorCode.includes(code)) {
    Message({
      message: msg,
      type: 'error',
    });
  }
  return Promise.reject(error);
};

/**
 * 创建请求实例
 * @param {*} baseURL
 */
function createAxios(baseURL = process.env.VUE_APP_PROXY) {
  // 创建请求实例
  const instance = axios.create({
    baseURL: baseURL,
    // 指定请求超时的毫秒数
    timeout: 90000,
    // 表示跨域请求时是否需要使用凭证
    withCredentials: false,
  });
  // 前置拦截器（发起请求之前的拦截）
  instance.interceptors.request.use(reqInterceptors, errorHandler);

  // 后置拦截器（获取到响应时的拦截）
  instance.interceptors.response.use(resInterceptors, errorHandler);
  return instance;
}

const service = createAxios();

export default service;

export { service };
