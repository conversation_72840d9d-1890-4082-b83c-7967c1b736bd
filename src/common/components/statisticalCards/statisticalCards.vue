<template>
  <div
    class="static-card"
    :style="[customBgStyle]"
  >
    <div class="item">{{ title }}</div>
    <div class="icon-row">
      <i :class="icon"></i>
      <span class="icon-value ellipsis">{{ value }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'statisticalCard',
  props: {
    title: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number],
      default: '',
    },
    icon: {
      type: String,
      default: '',
    },
    bg: {
      type: String,
      default: 'linear-gradient(160deg, #f60970, #da54a2)',
    },
  },
  computed: {
    customBgStyle() {
      return {
        background: this.bg,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.static-card {
  width: 250px;
  min-height: 100px;
  border-radius: 10px;
  color: #fff;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.icon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-row i {
  font-size: 28px;
  flex: 1;
  flex-shrink: 0;
}

.icon-value {
  font-size: 32px;
  margin-left: 10px;
  min-width: 0;
}

// .static-card-1 {
//   background: linear-gradient(160deg, #f60970, #da54a2);
// }

// .static-card-2 {
//   background: linear-gradient(160deg, #8f69ca, #483c9b);
// }

// .static-card-3 {
//   background: linear-gradient(160deg, #57d6f2, #4b97e8);
// }

// .static-card-4 {
//   background: linear-gradient(160deg, #fbc927, #f67852);
// }
.item {
  font-size: 1rem;
}
</style>
