<script lang="jsx">
export default {
  name: 'BaseTableColumn',
  props: {
    config: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    createComponent() {
      const { children = [], ...property } = this.config;
      if (children && children.length) {
        const attrs = this.productColumn(property);
        return (
          <el-table-column {...attrs}>
            {children.map((item, i) => {
              return (
                <base-table-column
                  config={item}
                  key={i}
                />
              );
            })}
          </el-table-column>
        );
      } else {
        const attrs = this.productColumn(property);
        return <el-table-column {...attrs}></el-table-column>;
      }
    },
    productColumn(item) {
      const attrs = {
        ...item,
        attrs: {
          align: 'center',
          'header-align': 'center',
          showOverflowTooltip: !['操作'].includes(item.label),
          ...item,
        },
      };
      return attrs;
    },
  },
  render() {
    return this.createComponent();
  },
};
</script>
