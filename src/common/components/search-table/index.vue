<template>
  <base-card class="search-table-container">
    <div v-if="filter">
      <el-row type="flex">
        <el-form
          ref="tableForm"
          :size="size"
          :inline="true"
          :model="form"
          class="table-form-inline"
          :labelWidth="labelWidth"
        >
          <div v-if="baseFields && baseFields.length">
            <el-form-item
              v-for="(item, index) of baseFields"
              v-show="item.show"
              :key="index"
              :prop="item.name"
              :label="item.label"
            >
              <div
                :class="{
                  'search-table-form-item': item.type && !item.type.includes('range'),
                }"
              >
                <common-form-item
                  :data="item"
                  :form="form"
                />
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="handleSearch"
              >
                搜索
              </el-button>
              <el-button
                type="info"
                @click="handleReset"
              >
                重置
              </el-button>
              <slot name="after" />
            </el-form-item>
          </div>
        </el-form>
      </el-row>
    </div>
    <div
      v-loading="loading"
      class="table-container"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <el-table
        ref="sTable"
        :data="data"
        :height="computedHeight"
        class="list-table"
        :row-key="rowKey"
        :lazy="lazy"
        :load="load"
        :tree-props="treeProps"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        @cell-click="cellClick"
      >
        <template v-for="(column, i) of baseColumns">
          <slot
            v-if="column.slot"
            name="columns"
          ></slot>
          <base-table-column
            v-else
            :key="i"
            :config="column"
          />
        </template>
      </el-table>
      <div
        v-if="pager"
        class="search-table-footer"
      >
        <el-pagination
          :current-page.sync="pageNum"
          :page-size="tablePageSize"
          :page-sizes="[10, 20, 50, 100]"
          :pager-count="pagerCount"
          background
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </base-card>
</template>
<script lang="jsx">
import BaseTableColumn from './components/base-table-column.vue';
export default {
  name: 'SearchTable',
  components: {
    BaseTableColumn,
  },
  props: {
    labelWidth: {
      type: String,
      default: '80px',
    },
    size: {
      type: String,
      default: 'small',
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper',
    },
    pager: {
      type: Boolean,
      default: true,
    },
    filter: {
      type: Boolean,
      default: true,
    },
    columns: {
      type: Array,
      default() {
        return [];
      },
    },
    height: {
      type: [String, Number],
      default: '520px',
    },
    autoHeight: {
      type: Boolean,
      default: true,
    },
    minHeight: {
      type: [String, Number],
      default: '200px',
    },
    maxHeight: {
      type: [String, Number],
      default: '600px',
    },
    rowHeight: {
      type: Number,
      default: 40,
    },
    data: {
      type: Array,
      default: () => [],
    },
    total: {
      type: Number,
      default: 0,
    },
    fields: {
      type: Array,
      default: () => [],
    },
    query: {
      type: [Boolean, Function],
    },
    form: {
      type: Object,
      default: () => {},
    },
    operate: {
      type: String,
      default: 'search',
      validator(val) {
        return ['search', 'add', 'edit', 'delete'].includes(val);
      },
      description: '用来决定表格是否只刷新当前数据还是从第一页刷新',
    },
    selRows: {
      type: Array,
      default: () => [],
    },
    rowKey: {
      type: [String, Function],
      default: '',
    },
    lazy: {
      type: Boolean,
      default: false,
    },
    load: {
      type: Function,
    },
    treeProps: {
      type: Object,
      default() {
        return { hasChildren: 'hasChildren', children: 'children' };
      },
    },
    pagerCount: {
      type: Number,
      default: 7,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      loading: false,
      tablePageSize: 10,
      pageNum: 1,
      copyForm: {},
      filterForm: {},
      sort: {},
      dialogXJ: false,
    };
  },
  computed: {
    baseFields() {
      return this.fields.map((item) => {
        return {
          clearable: true,
          filterable: true,
          show: true,
          ...item,
        };
      });
    },
    baseColumns() {
      return this.columns.map((item) => {
        return {
          ...item,
          sortable: item.sortable ? 'custom' : false,
        };
      });
    },
    computedHeight() {
      if (!this.autoHeight) {
        return this.height;
      }

      // 计算动态高度
      const dataLength = this.data ? this.data.length : 0;
      const headerHeight = 40; // 表头高度
      const calculatedHeight = headerHeight + dataLength * this.rowHeight;

      // 转换最小和最大高度为数字
      const minHeightNum =
        typeof this.minHeight === 'string' ? parseInt(this.minHeight.replace('px', '')) : this.minHeight;
      const maxHeightNum =
        typeof this.maxHeight === 'string' ? parseInt(this.maxHeight.replace('px', '')) : this.maxHeight;

      // 确保高度在最小值和最大值之间
      const finalHeight = Math.max(minHeightNum, Math.min(calculatedHeight, maxHeightNum));

      return `${finalHeight}px`;
    },
  },
  watch: {
    operate() {
      if (['add', 'delete'].includes(this.operate)) {
        this.pageNum = 1;
        this.getTableData();
      } else if (['edit'].includes(this.operate)) {
        this.getTableData();
      }
    },
    pageSize() {
      this.syncPageSize();
    },
  },
  created() {
    this.createdForm();
    this.createdFilterForm();
  },
  mounted() {
    this.syncPageSize();
    this.getTableData();
  },
  methods: {
    syncPageSize() {
      this.tablePageSize = this.pageSize;
    },
    // 生产过滤的form对象
    createdFilterForm() {
      this.filterForm = {};
      this.columns.forEach((item) => {
        if (item.filterable) {
          this.$set(this.filterForm, item.prop, []);
        }
      });
    },
    // 生产form对象
    createdForm() {
      this.copyForm = { ...this.form };
    },
    // 每页数量发生变化
    handleSizeChange(val) {
      this.pageNum = 1;
      this.tablePageSize = val;
      this.getTableData();
    },
    // 当前页发生变化
    handleCurrentChange() {
      this.getTableData();
    },
    // 搜索按钮点击
    handleSearch() {
      this.pageNum = 1;
      this.createdForm();
      this.getTableData();
    },
    //  重置按钮发生变化
    handleReset() {
      this.pageNum = 1;
      this.$refs.tableForm.resetFields();
      this.createdForm();
      for (const key in this.filterForm) {
        this.filterForm[key] = [];
      }
      this.$refs.sTable.clearSort();
      this.sort = {};
      this.getTableData();
      this.$emit('reset');
    },
    // 获取表格数据
    async getTableData() {
      this.$emit('update:operate', 'search');
      if (typeof this.query === 'function') {
        const filterObj = {};
        for (const key in this.filterForm) {
          filterObj[key] = this.filterForm[key].join(',');
        }
        const rows = {
          ...filterObj,
          ...this.sort,
          ...this.copyForm,
          pageNum: this.pageNum,
          pageSize: this.tablePageSize,
        };
        this.loading = true;
        try {
          await this.query(rows);
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      }
    },
    // 多选反生变化
    handleSelectionChange(rows) {
      this.$emit('update:selRows', rows);
    },
    // 清除选择的勾选
    clearSelection() {
      this.$refs.sTable.clearSelection();
      this.$emit('update:selRows', []);
    },
    // 用于多选表格，切换某一行的选中状态
    toggleRowSelection(row, selected) {
      this.$refs.sTable.toggleRowSelection(row, selected);
    },
    // 排序
    sortChange(column) {
      const sort = {};
      if (column.order) {
        sort.sortProperty = column.prop;
        sort.sortOrder = column.order === 'ascending' ? 'ASC' : 'DESC';
      }
      this.pageNum = 1;
      this.sort = { ...sort };
      this.getTableData();
    },
    // 当某个单元格被点击时会触发该事件
    cellClick(row, column, cell, event) {
      this.$emit('cell-click', row, column, cell, event);
    },
  },
};
</script>
<style lang="scss" scoped>
.search-table-container {
  background: #fff;
  padding: 10px;
}
.search-table-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px 0 0 0;
}
</style>
<style lang="scss">
.el-tooltip__popper {
  max-width: 40vw;
}
</style>
