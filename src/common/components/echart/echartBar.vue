<template>
  <div
    class="chart"
    ref="chart"
    style="width: 100%; height: 400px"
  ></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'echartBar',
  props: {
    options: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      const chartDom = this.$refs.chart;
      this.chart = echarts.init(chartDom);
      this.chart.setOption(this.options);
      window.addEventListener('resize', this.handleResize);
    },
    updateChart() {
      if (this.chart) {
        this.chart.setOption(this.options, true);
      }
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  watch: {
    options: {
      handler() {
        this.$nextTick(() => {
          this.updateChart();
        });
      },
      deep: true,
    },
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener('resize', this.handleResize);
  },
};
</script>

<style scoped lang="scss">
.chart {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 10px;
  background: #fff;
}
</style>
