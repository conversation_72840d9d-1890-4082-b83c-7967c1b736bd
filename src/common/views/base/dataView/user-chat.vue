<template>
  <div>
    <base-tips
      title="用户会话统计"
      class="mb-10"
    ></base-tips>
    <base-card class="mb-10">
      <div class="flex top-container">
        <statistical-cards
          title="今日|累计会话次数"
          :value="topData.data"
          icon="el-icon-s-data"
        ></statistical-cards>
        <!-- <statistical-cards
          title="今日|平均使用时长(min)"
          :value="topData.averageTime"
          icon="el-icon-s-data"
          bg="linear-gradient(160deg, #8f69ca, #483c9b)"
        ></statistical-cards> -->
      </div>
    </base-card>
    <base-tips
      title="详细数据"
      class="mb-10"
    ></base-tips>
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      height="340px"
      :total="total"
    >
      <el-button
        slot="after"
        @click="downloadFile"
        >导出</el-button
      >
    </search-table>
    <!-- 访问统计详情弹窗 -->
    <question-log
      :value.sync="show"
      v-if="show"
      :id="id"
      :time="form.time"
      :visit-from="dialogVisitFrom"
    ></question-log>
  </div>
</template>

<script>
import statisticalCards from '@/common/components/statisticalCards/statisticalCards.vue';
import { pageUserSession, getTodaySessionNum, exportUserSessionData } from '@/common/api/data';
import { SOURCE } from '@/common/utils/constant';
import { download } from '@/common/utils/index';
import questionLog from './components/question.vue';
export default {
  components: { statisticalCards, questionLog },
  data() {
    const query = this.queryTable;
    return {
      query,
      data: [],
      topData: {
        data: 0,
        averageTime: 0,
      },
      columns: [
        {
          prop: 'userCode',
          label: '工号',
        },
        {
          prop: 'visitFromName',
          label: '访问来源',
        },
        {
          prop: 'totalSessionCount',
          label: '提问次数',
          scopedSlots: {
            default: (scope) => {
              return (
                <el-button
                  type="text"
                  onClick={() => {
                    this.handleInfo(scope.row);
                  }}
                >
                  {scope.row.totalSessionCount}
                </el-button>
              );
            },
          },
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          tag: 'el-select',
          name: 'visitFrom',
          label: '访问来源',
          type: 'select',
          childTag: 'el-option',
          options: SOURCE,
        },
      ],
      form: {
        time: '',
        visitFrom: '',
      },
      total: 0,
      show: false,
      id: '',
      dialogVisitFrom: '',
    };
  },
  mounted() {
    this.queryTopData();
  },
  methods: {
    /**搜索table数据 */
    async queryTable(row) {
      const params = {
        page: {
          pageNum: row.pageNum,
          pageSize: row.pageSize,
        },
        filter: {
          startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : '',
          endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : '',
          visitFrom: row.visitFrom,
        },
      };
      const res = await pageUserSession(params);
      if (res && res.code === 200) {
        this.data = res.data.dataList.map((item) => {
          const visitFromName = SOURCE.find((i) => i.value === item.visitFrom);
          return {
            ...item,
            visitFromName: visitFromName && visitFromName.label,
          };
        });
        console.log('接口返回数据', res.data);
        this.total = res.data.totalCount;
      }
    },
    /**顶部数据查询 */
    async queryTopData() {
      const res = await getTodaySessionNum();
      if (res && res.code === 200) {
        this.topData.data = res.data;
      }
    },
    async downloadFile() {
      const row = this.form;
      const params = {
        startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : '',
        endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : '',
        visitFrom: row.visitFrom,
      };
      const res = await exportUserSessionData(params);
      if (res && res.status === 200) {
        download(res.data, '用户会话统计-详细数据列表');
      }
      console.log(res);
    },
    /**查看访问次数详情 */
    handleInfo(row) {
      this.show = true;
      this.id = row.userCode;
      this.dialogVisitFrom = row.visitFrom;
    },
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}
</style>
