<template>
  <div>
    <div class="realtime-header mb-10">
      <base-tips
        title="推送管理"
        class="mb-10"
      >
        <div slot="title">
          <span>推送管理</span>
        </div>
      </base-tips>
    </div>
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      :total="total"
    >
      <el-button
        slot="after"
        @click="downloadFile"
        >导出</el-button
      >
    </search-table>
  </div>
</template>

<script>
import {
  getPointsdetailsTodaysum,
  listTop20UserName,
  getPointsdetailsList,
  pointsdetailsExportExcel,
} from '@/common/api/data';
import { SOURCE } from '@/common/utils/constant';
import { download } from '@/common/utils/index';
export default {
  data() {
    const query = this.queryTable;
    return {
      query,
      data: [],
      topData: 0,
      columns: [
        {
          prop: 'userCode',
          label: '推送时间',
        },
        {
          prop: 'userName',
          label: '推送类型',
        },
        {
          prop: 'pointsName',
          label: '推送内容(标题)',
        },
        {
          prop: 'pointsPerTransaction',
          label: '用户总数',
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          tag: 'el-select',
          name: 'userName',
          label: '推送内容',
          type: 'select',
          childTag: 'el-option',
          options: [],
          filterable: true,
          remote: true,
          reserveKeyword: true,
          remoteMethod: this.remoteSearchUser,
          loading: false,
          placeholder: '请输入',
        },
      ],
      form: {
        time: '',
        visitFrom: '',
        userName: '',
      },
      total: 0,
      realtimeText: '',
      onlineUser: 456,
      userOptions: [], // 存储用户选项
    };
  },
  async mounted() {
    // 初始加载一些用户数据
    await this.remoteSearchUser('');
    this.queryTopData();
  },
  methods: {
    /**
     * 远程搜索用户
     * @param {string} query 搜索关键词
     */
    async remoteSearchUser(query) {
      // 设置对应字段的loading状态
      this.fields = this.fields.map((item) => {
        if (item.name === 'userName') {
          item.loading = true;
        }
        return item;
      });

      try {
        // 调用API获取用户列表
        const res = await listTop20UserName(query);

        if (res && res.code === 200) {
          const options = res.data.map((item) => ({
            label: item.userName,
            value: item.userCode,
          }));

          // 更新用户选项
          this.userOptions = options;

          // 更新字段的options和loading状态
          this.fields = this.fields.map((item) => {
            if (item.name === 'userName') {
              item.options = [...options];
              item.loading = false;
            }
            return item;
          });
        }
      } catch (error) {
        console.error('获取用户列表失败:', error);
        // 出错时也要关闭loading状态
        this.fields = this.fields.map((item) => {
          if (item.name === 'userName') {
            item.loading = false;
          }
          return item;
        });
      }
    },

    /**搜索table数据 */
    async queryTable(row) {
      const params = {
        page: {
          pageNum: row.pageNum,
          pageSize: row.pageSize,
        },
        filter: {
          startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : null,
          endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : null,
          visitFrom: row.visitFrom || null,
          userName: row.userName || null,
        },
      };
      const res = await getPointsdetailsList(params);
      if (res && res.code === 200) {
        this.data = res.data.dataList.map((item) => {
          // 查找对应的访问来源标签
          const visitFromObj = SOURCE.find((i) => i.value === item.visitFrom);
          return {
            ...item,
            // 添加访问来源的汉字标签
            visitFromName: visitFromObj ? visitFromObj.label : item.visitFrom,
          };
        });
        this.total = res.data.totalCount;
      }
    },
    /**顶部数据查询 */
    async queryTopData() {
      const res = await getPointsdetailsTodaysum();
      if (res && res.code === 200) {
        this.topData = res.data;
      }
    },
    async downloadFile() {
      const row = this.form;
      const params = {
        startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : null,
        endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : null,
        visitFrom: row.visitFrom || null,
        userName: row.userName || null,
      };
      const res = await pointsdetailsExportExcel(params);
      // console.log(res.data);
      if (res && res.status === 200) {
        download(res.data, '消息推送管理-详细数据列表');
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;

  // .top-item {
  //   width: 25%;
  //   height: 100px;
  // }
}

.mb-10 {
  margin-bottom: 10px;
}

.realtime-info {
  color: #909399;
  margin-left: 10px;
  font-size: 14px;
  vertical-align: middle;
}
.chartBox {
  overflow: hidden;
  margin-top: 10px;
  .chartBoxContent {
    width: calc(50% - 5px);
    float: left;
    &:nth-child(odd) {
      margin-right: 10px;
    }
  }
}
</style>
