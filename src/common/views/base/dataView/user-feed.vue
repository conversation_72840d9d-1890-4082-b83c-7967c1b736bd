<template>
  <div>
    <base-tips
      title="用户反馈"
      class="mb-10"
    ></base-tips>
    <base-card class="mb-10">
      <div class="flex top-container">
        <statistical-card
          title="今日|累积点赞数"
          :value="0"
          icon="el-icon-s-data"
          bg="linear-gradient(160deg, #f60970, #da54a2)"
        />
        <statistical-card
          title="今日|累积点踩数"
          :value="0"
          icon="el-icon-s-data"
          bg="linear-gradient(160deg, #8f69ca, #483c9b)"
        />
      </div>
    </base-card>
    <base-tips
      title="详细数据"
      class="mb-10"
    ></base-tips>
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      :total="0"
      height="340px"
    >
      <el-button slot="after">导出</el-button>
    </search-table>
  </div>
</template>

<script>
import statisticalCard from '@/common/components/statisticalCards/statisticalCards.vue';

export default {
  components: {
    statisticalCard,
  },
  data() {
    const query = this.queryTable;
    return {
      query,
      data: [],
      columns: [
        {
          prop: 'a',
          label: '工号',
        },
        {
          prop: 'a',
          label: '点赞/点踩',
        },
        {
          prop: 'a',
          label: '问题',
        },
        {
          prop: 'a',
          label: 'AI回复结果',
        },
        {
          prop: 'a',
          label: '点踩反馈',
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          tag: 'el-select',
          name: 'type',
          label: '访问来源',
          type: 'select',
          childTag: 'el-option',
          options: [],
        },
      ],
      form: {
        time: '',
        type: '',
      },
      total: 0,
    };
  },
  methods: {
    /**搜索table数据 */
    queryTable(row) {
      console.log(row, '参数');
    },
  },
};
</script>
<style lang="scss">
.top-container {
  gap: 10px;

  // .top-item {
  //   width: 25%;
  //   height: 100px;
  // }
}

.mb-10 {
  margin-bottom: 10px;
}
</style>
