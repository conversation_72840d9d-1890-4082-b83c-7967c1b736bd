<template>
  <div>
    <div class="realtime-header mb-10">
      <!-- <img
        src="http://************:9000/feedback/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250618174229_20250618054259.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20250619%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Date=20250619T074910Z&X-Amz-Expires=60&X-Amz-SignedHeaders=host&X-Amz-Signature=67dbe40ec51195487f3ab625f5a5360ba9d3d16e02eebb0e57dd98045b53725a"
      /> -->
      <base-tips
        title="反馈管理"
        class="mb-10"
      >
        <div slot="title">
          <span>反馈管理</span>
        </div>
      </base-tips>
    </div>
    <base-card class="mb-10">
      <div class="flex top-container">
        <statistical-cards
          title="今日|反馈数量"
          :value="topData"
          icon="el-icon-s-data"
        ></statistical-cards>
      </div>
    </base-card>
    <base-tips
      title="详细数据"
      class="mb-10"
    ></base-tips>
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      height="340px"
      :total="total"
    >
      <template slot="columns">
        <el-table-column
          label="图片"
          width="120"
        >
          <template slot-scope="{ row }">
            <div
              v-if="row.imageUrls && row.imageUrls.length > 0"
              class="image-preview-wrapper"
            >
              <!-- {{ row.imageUrls[0] }} -->
              <el-image
                style="width: 50px; height: 50px; object-fit: cover; cursor: pointer"
                :src="row.imageUrls[0]"
                :preview-src-list="row.imageUrls"
                fit="cover"
              >
                <!-- <div
                  slot="error"
                  class="image-slot"
                >
                  <i class="el-icon-picture-outline"></i>
                </div> -->
              </el-image>
              <!-- <span
                v-if="row.imageUrls.length > 1"
                class="image-count"
                >+{{ row.imageUrls.length - 1 }}</span
              > -->
            </div>
            <span v-else>无图片</span>
          </template>
        </el-table-column>
      </template>

      <el-button
        slot="after"
        @click="downloadFile"
        >导出</el-button
      >
    </search-table>
  </div>
</template>

<script>
import statisticalCards from '@/common/components/statisticalCards/statisticalCards.vue';
import { getFeedbackCounts, listTop20UserName, getUserfeedbackList, userfeedbackExportExcel } from '@/common/api/data';
import { SOURCE } from '@/common/utils/constant';
import { download } from '@/common/utils/index';
export default {
  components: { statisticalCards },
  data() {
    const query = this.queryTable;
    return {
      query,
      data: [],
      topData: 0,
      columns: [
        {
          prop: 'userCode',
          label: '工号',
        },
        {
          prop: 'userName',
          label: '姓名',
        },
        {
          prop: 'feedbackContent',
          label: '反馈内容',
        },
        {
          prop: 'imageUrls',
          label: '图片',
          slot: true,
        },
        {
          prop: 'visitFromName',
          label: '访问来源',
        },
        {
          prop: 'createTime',
          label: '时间',
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          tag: 'el-select',
          name: 'visitFrom',
          label: '访问来源',
          type: 'select',
          childTag: 'el-option',
          options: SOURCE,
        },
        {
          tag: 'el-select',
          name: 'userName',
          label: '用户姓名',
          type: 'select',
          childTag: 'el-option',
          options: [],
          filterable: true,
          remote: true,
          reserveKeyword: true,
          remoteMethod: this.remoteSearchUser,
          loading: false,
          placeholder: '请输入用户姓名或工号',
        },
      ],
      form: {
        time: '',
        visitFrom: '',
        userName: '',
      },
      total: 0,
      realtimeText: '',
      onlineUser: 456,
      userOptions: [], // 存储用户选项
    };
  },
  async mounted() {
    // 初始加载一些用户数据
    await this.remoteSearchUser('');
    this.queryTopData();
  },
  methods: {
    /**
     * 远程搜索用户
     * @param {string} query 搜索关键词
     */
    async remoteSearchUser(query) {
      // 设置对应字段的loading状态
      this.fields = this.fields.map((item) => {
        if (item.name === 'userName') {
          item.loading = true;
        }
        return item;
      });

      try {
        // 调用API获取用户列表
        const res = await listTop20UserName(query);

        if (res && res.code === 200) {
          const options = res.data.map((item) => ({
            label: item.userName,
            value: item.userCode,
          }));

          // 更新用户选项
          this.userOptions = options;

          // 更新字段的options和loading状态
          this.fields = this.fields.map((item) => {
            if (item.name === 'userName') {
              item.options = [...options];
              item.loading = false;
            }
            return item;
          });
        }
      } catch (error) {
        console.error('获取用户列表失败:', error);
        // 出错时也要关闭loading状态
        this.fields = this.fields.map((item) => {
          if (item.name === 'userName') {
            item.loading = false;
          }
          return item;
        });
      }
    },

    /**搜索table数据 */
    async queryTable(row) {
      const params = {
        page: {
          pageNum: row.pageNum,
          pageSize: row.pageSize,
        },
        filter: {
          startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : null,
          endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : null,
          visitFrom: row.visitFrom || null,
          userName: row.userName || null,
        },
      };
      const res = await getUserfeedbackList(params);
      if (res && res.code === 200) {
        this.data = res.data.dataList.map((item) => {
          // 查找对应的访问来源标签
          const visitFromObj = SOURCE.find((i) => i.value === item.visitFrom);
          return {
            ...item,
            // 添加访问来源的汉字标签
            visitFromName: visitFromObj ? visitFromObj.label : item.visitFrom,
          };
        });
        this.total = res.data.totalCount;
      }
    },
    /**顶部数据查询 */
    async queryTopData() {
      const res = await getFeedbackCounts();
      if (res && res.code === 200) {
        this.topData = res.data;
      }
    },
    async downloadFile() {
      const row = this.form;
      const params = {
        startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : null,
        endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : null,
        visitFrom: row.visitFrom || null,
        userName: row.userName || null,
      };
      const res = await userfeedbackExportExcel(params);
      // console.log(res.data);
      if (res && res.status === 200) {
        download(res.data, '反馈管理-详细数据列表');
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.realtime-info {
  color: #909399;
  margin-left: 10px;
  font-size: 14px;
  vertical-align: middle;
}

.image-preview-wrapper {
  position: relative;
  display: inline-block;
}

.image-count {
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.chartBox {
  overflow: hidden;
  margin-top: 10px;
  .chartBoxContent {
    width: calc(50% - 5px);
    float: left;
    &:nth-child(odd) {
      margin-right: 10px;
    }
  }
}
</style>
