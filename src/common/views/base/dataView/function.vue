<template>
  <div>
    <base-tips
      title="功能使用统计"
      class="mb-10"
    ></base-tips>
    <search-table
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      height="560px"
      :total="total"
      :pager="false"
    >
      <el-button
        slot="after"
        @click="downloadFile"
        >导出</el-button
      >
    </search-table>
  </div>
</template>

<script>
import { getDocumentUsage, exportDocumentUsageExcel, getSessionTypes } from '@/common/api/data';
import { SOURCE } from '@/common/utils/constant';
import { download } from '@/common/utils/index';
import { formatDate } from 'element-ui/lib/utils/date-util';
export default {
  data() {
    const query = this.queryTable;
    const now = new Date().getTime();
    const start = now - 10 * 24 * 60 * 60 * 1000;
    const startTime = formatDate(start, 'yyyy-MM-dd');
    const endTime = formatDate(now, 'yyyy-MM-dd');
    return {
      query,
      data: [],
      columns: [
        {
          prop: 'typeName',
          label: '功能名称',
        },
        {
          prop: 'visitFromName',
          label: '访问来源',
        },
        {
          prop: 'count',
          label: '使用次数',
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          tag: 'el-select',
          name: 'visitFrom',
          label: '访问来源',
          type: 'select',
          childTag: 'el-option',
          options: SOURCE,
        },
        {
          tag: 'el-select',
          name: 'sessionType',
          label: '功能名称',
          type: 'select',
          childTag: 'el-option',
          options: [],
        },
      ],
      form: {
        time: [startTime, endTime],
        type: '',
        sessionType: '',
      },
      total: 0,
    };
  },
  methods: {
    /**搜索table数据 */
    async queryTable(row) {
      const params = {
        startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : '',
        endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : '',
        visitFrom: row.visitFrom,
        sessionType: row.sessionType,
      };
      const res = await getDocumentUsage(params);
      console.log(res);
      if (res && res.code === 200) {
        this.data = [];
        res.data.forEach((item) => {
          const visitFromName = SOURCE.find((i) => i.value === item.visitFrom);
          item.sessionTypeCounts.forEach((el) => {
            this.data.push({
              ...el,
              visitFromName: visitFromName?.label,
            });
          });
        });
      }
    },
    async downloadFile() {
      const row = this.form;
      const params = {
        startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : '',
        endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : '',
        visitFrom: row.visitFrom,
      };
      const res = await exportDocumentUsageExcel(params);
      if (res && res.status === 200) {
        download(res.data, '功能使用统计-详细数据列表');
      }
      console.log(res);
    },
  },
  async mounted() {
    const res = await getSessionTypes();
    console.log(res, 'res');
    if (res && res.code === 200) {
      const options = res.data.map((item) => ({
        label: item.value,
        value: item.name,
      }));
      this.fields = this.fields.map((item) => {
        if (item.name === 'sessionType') {
          item.options = [...options];
        }
        return item;
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.top-container {
  gap: 10px;
  .top-item {
    width: 25%;
    height: 100px;
  }
}
.mb-10 {
  margin-bottom: 10px;
}
</style>
