<template>
  <el-dialog
    title="访问详情"
    :visible.sync="dialogVisible"
    width="1000px"
    :before-close="handleClose"
  >
    <search-table
      :shadow="false"
      :form="form"
      :fields="fields"
      :columns="columns"
      :data="data"
      :query="query"
      height="340px"
      :total="total"
    >
    </search-table>
  </el-dialog>
</template>
<script>
import { getChatRecordList } from '@/common/api/data';
import { SOURCE } from '@/common/utils/constant';
export default {
  name: 'questionLog',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: '',
    },
    time: {
      type: [Array, String],
      default: () => [],
    },
    visitFrom: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    const query = this.queryTable;
    return {
      query,
      data: [],
      topData: {
        data: 0,
        averageTime: 0,
      },
      columns: [
        {
          prop: 'userCode',
          label: '工号',
        },
        {
          prop: 'sessionTitle',
          label: '提问内容',
        },
        {
          prop: 'visitFromName',
          label: '访问来源',
        },
        {
          prop: 'sessionTime',
          label: '提问时间',
        },
      ],
      fields: [
        {
          tag: 'el-date-picker',
          name: 'time',
          label: '查询时间',
          type: 'daterange',
          valueFormat: 'yyyy-MM-dd',
        },
        {
          tag: 'el-select',
          name: 'visitFrom',
          label: '访问来源',
          type: 'select',
          childTag: 'el-option',
          options: SOURCE,
        },
      ],
      form: {
        time: '',
        visitFrom: '',
      },
      total: 0,
      dialogVisible: false,
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    async queryTable(row) {
      const params = {
        page: {
          pageNum: row.pageNum,
          pageSize: row.pageSize,
        },
        filter: {
          startTime: row.time && row.time[0] ? `${row.time[0]} 00:00:00` : '',
          endTime: row.time && row.time[1] ? `${row.time[1]} 23:59:59` : '',
          visitFrom: row.visitFrom,
          userCode: this.id,
        },
      };
      const res = await getChatRecordList(params);
      if (res && res.code === 200) {
        this.data = res.data.dataList.map((item) => {
          const visitFrom = SOURCE.find((i) => i.value === item.visitFrom);
          return {
            ...item,
            visitFromName: visitFrom && visitFrom.label,
          };
        });
        console.log('接口返回数据', res.data);
        this.total = res.data.totalCount;
      }
      console.log(res, '信息');
    },
    initData() {
      this.dialogVisible = this.value;
      if (this.time && this.time.length) {
        this.form.time = this.time;
      }
      if (this.visitFrom) {
        this.form.visitFrom = this.visitFrom;
      }
    },
    handleClose() {
      this.dialogVisible = false;
      this.$emit('update:value', false);
    },
  },
};
</script>
