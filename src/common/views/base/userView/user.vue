<template>
  <div>
    <search-table
      :fields="fields"
      :query="queryTable"
      :data="data"
      :form="form"
      :columns="columns"
      :total="total"
      labelWidth="60px"
    />
    <!-- 用户信息弹窗 -->
    <user-info
      v-if="showUser"
      :info="info"
      :visible.sync="showUser"
    />
  </div>
</template>

<script>
import { getUserList } from '@/common/api/data';
import { USER_STATUS } from '@/common/utils/constant';
import UserInfo from './components/user-info.vue';
export default {
  components: {
    UserInfo,
  },
  data() {
    const query = this.queryTable;
    return {
      query,
      data: [],
      form: {
        userCode: '',
        userName: '',
        phonenumber: '',
      },
      fields: [
        {
          tag: 'el-input',
          label: '登录名',
          name: 'userCode',
          type: 'input',
          placeholder: '请输入登录名',
        },
        {
          tag: 'el-input',
          label: '姓名',
          name: 'userName',
          type: 'input',
          placeholder: '请输入 姓名',
        },
        // {
        //   tag: 'el-input',
        //   label: '手机号',
        //   name: 'phonenumber',
        //   type: 'input',
        //   placeholder: '请输入 手机号码',
        // },
      ],
      // 表格列配置
      columns: [
        { type: 'index', label: '序号', width: '40px' },
        { prop: 'userCode', label: '登录名' },
        { prop: 'userName', label: '姓名' },
        { prop: 'phonenumber', label: '手机号' },
        {
          prop: 'userStatusName',
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.userStatusName) {
                return (
                  <el-tag type={scoped.row.userStatusName === '正常' ? '' : 'danger'}>
                    {scoped.row.userStatusName}
                  </el-tag>
                );
              }
            },
          },
        },
        { prop: 'createTime', label: '创建时间' },
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  type="text"
                  size="mini"
                  onClick={() => this.queryInfo(scoped.row)}
                >
                  查看
                </el-button>
              );
            },
          },
        },
      ],
      total: 0,
      showUser: false,
      info: {},
    };
  },
  methods: {
    /**搜索table数据 */
    async queryTable(row) {
      const params = {
        page: {
          pageNum: row.pageNum,
          pageSize: row.pageSize,
        },
        filter: {
          userCode: row.userCode,
          userName: row.userName,
          phonenumber: row.phonenumber,
        },
      };
      const res = await getUserList(params);
      if (res && res.code === 200) {
        this.data = res.data.dataList.map((item) => {
          const userStatusName = USER_STATUS.find((el) => el.value === item.userStatus);
          item.userStatusName = userStatusName?.label;
          return item;
        });
        this.total = res.data.totalCount;
      }
    },
    /**查看用户详情 */
    async queryInfo(row) {
      this.showUser = true;
      this.info = { ...row };
    },
  },
};
</script>
