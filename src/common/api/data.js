import service from '@/common/utils/request';

/**
 * 实时数据路由下的详细数据列表
 * @param {*} params
 * @returns
 */
export const getRealDataList = (params) => {
  return service.post('/api/dailyvisit/page/userbehavior', params);
};

/**
 * 实时数据路由下今日数据统计
 * @param {*} params
 * @returns
 */
export const getRealTodayDataInfo = (params) => {
  return service.get('/api/userbehavior/today/statistics', params);
};

/**
 *  实时数据路由下的导出实时数据
 * @param {*} params
 * @returns
 */
export const downloadRealData = (params) => {
  return service.post('/api/dailyvisit/export/userbehavior/excel', params, {
    responseType: 'blob',
  });
};

/**
 *当日累计会话次数
 * @param {*} params
 * @returns
 */
export const getTodaySessionNum = (params) => {
  return service.get('/api/usersession/todaysessionnum', params);
};

/**
 *用户会话统计-详细数据
 * @param {*} params
 * @returns
 */
export const pageUserSession = (params) => {
  return service.post('/api/usersessionstatistics/page/usersession', params);
};

/**
 *用户会话统计-导出详细数据
 * @param {*} params
 * @returns
 */
export const exportUserSessionData = (params) => {
  return service.post('/api/usersessionstatistics/export/excel', params, {
    responseType: 'blob',
  });
};

/**
 *功能使用统计-详细数据
 * @param {*} params
 * @returns
 */
export const getDocumentUsage = (params) => {
  return service.post('/api/userStatistics/getDocumentUsage', params);
};

/**
 *功能使用统计-导出详细数据
 * @param {*} params
 * @returns
 */
export const exportDocumentUsageExcel = (params) => {
  return service.post('/api/userStatistics/exportDocumentUsageExcel', params, {
    responseType: 'blob',
  });
};

/**
 * 获取系统用户列表
 * @param {*} params
 * @returns
 */
export const getUserList = (params) => {
  return service.post('/api/userManage/getSystemUserInfo', params);
};

/**
 *获取访问来源列表
 * @param {*} params
 * @returns
 */
export const getSessionTypes = (params) => {
  return service.get('/api/userStatistics/getSessionTypes', params);
};

/**
 * 获取会话记录列表
 * @param {*} params
 * @returns
 */
export const getChatRecordList = (params) => {
  return service.post('api/usersession/page/details', params);
};

/**
 *积分管理-今日积分发放数量
 * @param {*} params
 * @returns
 */
export const getPointsdetailsTodaysum = (params) => {
  return service.get('/api/pointsdetails/todaysum', params);
};

/**
 * 积分管理-详细数据-用户姓名下拉框（工号和姓名都支持模糊查询）
 * @param {string} userName 用户名查询关键词
 * @returns {Promise} API响应
 */
export const listTop20UserName = (userName = '') => {
  return service.get(`/api/sysuser/list/top20?userName=${encodeURIComponent(userName)}`);
};

/**
 * 积分管理-详细数据列表
 * @param {*} params
 * @returns
 */
export const getPointsdetailsList = (params) => {
  return service.post('/api/pointsdetails/page', params);
};

/**
 * 积分管理-详细数据列表导出
 * @param {*} params
 * @returns
 */
export const pointsdetailsExportExcel = (params) => {
  return service.post('/api/pointsdetails/export/excel', params, {
    responseType: 'blob',
  });
};

// 积分管理-日活用户数量表
export const getUserChartsStats = (params) => {
  return service.post('/api/dailyvisit/getUserChartsStats', params);
};
// 积分管理-日访问数量表
export const getVisitChartsStats = (params) => {
  return service.post('/api/uservisit/getVisitChartsStats', params);
};

/**
 *反馈管理-今日积分发放数量
 * @param {*} params
 * @returns
 */
export const getFeedbackCounts = (params) => {
  return service.get('/api/userfeedback/getFeedbackCounts', params);
};
/**
 * 反馈管理-详细数据列表
 * @param {*} params
 * @returns
 */
export const getUserfeedbackList = (params) => {
  return service.post('/api/userfeedback/page', params);
};
/**
 * 反馈管理-详细数据列表导出
 * @param {*} params
 * @returns
 */
export const userfeedbackExportExcel = (params) => {
  return service.post('/api/userfeedback/export', params, {
    responseType: 'blob',
  });
};
