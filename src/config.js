//const isWindowAvailable = typeof window !== 'undefined';
const isCloud = process.env.VUE_APP_IS_CLOUD == 'false' ? false : process.env.VUE_APP_IS_CLOUD;

module.exports = {
  title: '天易开发平台', // 平台标题
  theme: '#4871C0', // 默认主题色
  isCloud: isCloud, //是否是微服务
  publicPath: '/', // 静态资源路径
  isShowLayoutSetting: true, // 是否显示布局设置
  layoutType: 'edge', // 默认布局类型 side/top/edge
  isShowTagsSetting: true, // 是否显示页签样式设置
  tagsStyle: 'tab', // 默认标签风格 line/tab
  // URI:
  //   isWindowAvailable && window.BASE_URL
  //     ? window.BASE_URL
  //     : `http://gateway.dev.snpit.com:${isCloud ? 8080 : 8920}`, // 接口地址
  URI: process.env.NODE_ENV === 'development' ? 'http://reasoningai-test.spic.com.cn/qaapi' : '',
};
