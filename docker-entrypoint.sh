#!/usr/bin/env bash

API_BASE_PATH=$API_BASE_PATH;
BASE_URL=$BASE_URL;
MINIO_BASE_PATH=$MINIO_BASE_PATH;
BPM_URL=$BPM_URL;
MESSAGE_URL=$MESSAGE_URL;
if [ -z "$API_BASE_PATH" ]; then
    API_BASE_PATH="http://gateway.dev.snpit.com:8080/";
fi

if [ -z "$MINIO_BASE_PATH" ]; then
    API_BASE_PATH="http://minio.dev.snpit.com:9000/";
fi

if [ -z "$BASE_URL" ]; then
    BASE_URL=$API_BASE_PATH;
fi
if [ -z "$BPM_URL" ]; then
    BASE_URL="http://ui.bpm.dev.snpit.com:9999/";
fi
if [ -z "$MESSAGE_URL" ]; then
    BASE_URL="http://ui.message.dev.snpit.com:9106/";
fi


apiUrl="proxy_pass $API_BASE_PATH"
minioUrl="proxy_pass $MINIO_BASE_PATH"
webMessageUrl="proxy_pass $MESSAGE_URL"
bpmUrl="proxy_pass $BPM_URL"


sed -i '6c '"$apiUrl;"'' /etc/nginx/conf.d/default.conf
sed -i '17c '"$bpmUrl;"'' /etc/nginx/conf.d/default.conf
sed -i '22c '"$webMessageUrl;"'' /etc/nginx/conf.d/default.conf
sed -i '30c '"$minioUrl;"'' /etc/nginx/conf.d/default.conf
#sed -i 's~window\.BASE_URL="[^"]*"~window.BASE_URL="'"$BASE_URL"'"~1' /opt/isimple/web/index.html
nginx -g "daemon off;"
